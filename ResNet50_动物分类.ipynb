import torch.optim as optim 
import torch
import torch.nn as nn 
import torch.nn.parallel 
import torch.optim 
import torch.utils.data
import torch.utils.data.distributed
import torchvision.transforms as transforms 
import torchvision.datasets as datasets 
import torchvision.models
from torch.autograd import Variable

import os
from PIL import Image

DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu') 
DEVICE

# 数据预处理
#训练集数据增强
transform_train = transforms.Compose([ transforms.Resize((224, 224)), transforms.ToTensor(),
transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
])
#验证集数据增强
transform_val = transforms.Compose([ transforms.Resize((224, 224)), transforms.ToTensor(),
transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
])
transform_train

import shutil
modellr = 1e-4          # 学习率设置为 0.0001
BATCH_SIZE = 64         # 每批训练的样本数
EPOCHS = 20             # 训练轮数
# 删除隐藏文件/文件夹
for root, dirs, files in os.walk('./dataset'):
    for file in files:
        if 'ipynb_checkpoints' in file: os.remove(os.path.join(root, file))
    if 'ipynb_checkpoints' in root: shutil.rmtree(root)

# 读取数据
dataset_train = datasets.ImageFolder('./dataset/train', transform_train) 
print(dataset_train.imgs) # 对应文件夹的label
print(dataset_train.class_to_idx)  # 类别到索引的映射，如 {'cat': 0, 'dog': 1}
val_dataset =datasets.ImageFolder('./dataset/val', transform_val) 
print(val_dataset.class_to_idx)
# 导入数据,将数据集打包为 DataLoader，便于后续训练过程中分批加载。
train_loader = torch.utils.data.DataLoader(dataset_train, batch_size=BATCH_SIZE, shuffle=True)
val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False)

model =torchvision.models.resnet50(pretrained=False)
model

# 实例化模型并且移动到GPU
criterion = nn.CrossEntropyLoss() #定义损失函数
num_ftrs = model.fc.in_features #获取输入维度，通常是2048
model.fc = nn.Linear(num_ftrs, 2) #替换成输出为 2 的线性层，适配二分类任务
model.to(DEVICE)
# 选择简单暴力的Adam 优化器，学习率调低
optimizer = optim.Adam(model.parameters(), lr=modellr)


def adjust_learning_rate(optimizer, epoch):
    """Sets the learning rate to the initial LR decayed by 10 every 30 epochs"""
    modellrnew = modellr * (0.1 ** (epoch // 50)) 
    print("lr:", modellrnew)
    for param_group in optimizer.param_groups: #每个 param_group 代表一个优化器参数组
        param_group['lr'] = modellrnew

# 定义训练过程
def train(model, device, train_loader, optimizer, epoch): 
    model.train()
    sum_loss = 0 #这一轮的损失值
    train_acc = 0 ##这一轮的准确率
    total_num = len(train_loader.dataset) #样本总数
    # print(total_num, len(train_loader))
    for batch_idx, (data, target) in enumerate(train_loader): #批次索引，图像张量，标签
        data, target = data.to(device), target.to(device) 
        output = model(data)
        loss = criterion(output, target) 
        #清空旧梯度 → 反向传播 → 更新参数
        optimizer.zero_grad() 
        loss.backward()
        optimizer.step()
        print_loss = loss.data.item() 
        sum_loss += print_loss
        out_t = output.argmax(dim=1) #取出预测的最大值 
        num_correct = (out_t == target).sum().item() 
        acc = num_correct /  data.size(0)
        train_acc += acc

        if (batch_idx + 1) % 50 == 0:
            print('Train Epoch: {} [{}/{} ({:.0f}%)]\tLoss:{:.6f}'.format(
            epoch, (batch_idx + 1) * len(data), len(train_loader.dataset),
            100. * (batch_idx + 1) / len(train_loader), loss.item()))
            #👆当前训练进度（百分比）
    ave_loss = sum_loss / len(train_loader) 
    ave_acc = train_acc / len(train_loader)
    print('epoch:{}, train_acc: {}, loss:{}'.format(epoch, ave_acc,ave_loss))
    return ave_acc, ave_loss

def val(model, device, val_loader): 
    model.eval()#评估模式
    val_loss = 0
    correct = 0 #预测正确的样本数
    total_num = len(val_loader.dataset) 
    # print(total_num, len(test_loader)) 
    with torch.no_grad():#不需要反向传播，所以关闭 autograd
        for data, target in val_loader:#每一batch
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)
            _, pred = torch.max(output.data, 1) #pred赋值每张图在所有类别中得分最大的类别的索引
            correct += torch.sum(pred == target) 
            print_loss = loss.data.item() 
            val_loss += print_loss
        correct = correct.data.item() 
        acc = correct / total_num
        avgloss = val_loss / len(val_loader)
        print('Val set: Average loss: {:.4f}, Accuracy: {}/{} ({:.0f}%)\n'.format(
            avgloss, correct, len(val_loader.dataset), 100 * acc)) 
    return acc, avgloss

train_acc_list, train_loss_list, val_acc_list, val_loss_list = [], [], [], []

for epoch in range(1, EPOCHS + 1): 
    adjust_learning_rate(optimizer, epoch)#优化，调整学习率
    train_acc, train_loss = train(model, DEVICE, train_loader, optimizer, epoch)#模型训练
    val_acc, val_loss = val(model, DEVICE, val_loader) #验证模型
    train_acc_list.append(train_acc) 
    val_acc_list.append(val_acc) 
    train_loss_list.append(train_loss) 
    val_loss_list.append(val_loss)
torch.save(model, 'model.pth')
import pickle
with open("training_result.pkl", "wb") as f:
    pickle.dump({
        "train_acc": train_acc_list,
        "val_acc": val_acc_list,
        "train_loss": train_loss_list,
        "val_loss": val_loss_list
    }, f)

import pickle

with open("training_result.pkl", "rb") as f:
    data = pickle.load(f)

train_acc_list = data["train_acc"]
val_acc_list = data["val_acc"]
train_loss_list = data["train_loss"]
val_loss_list = data["val_loss"]

import matplotlib.pyplot as plt
EPOCHS = 20
epochs_range = range(EPOCHS) 
print(epochs_range, train_acc_list)

plt.figure(figsize=(12, 4))
plt.subplot(1, 2, 1)
plt.plot(epochs_range, train_acc_list, label='Training Accuracy') 
plt.plot(epochs_range, val_acc_list, label='Validation Accuracy')
plt.legend(loc='lower right')
plt.title('Training and Validation Accuracy')

plt.subplot(1, 2, 2)
plt.plot(epochs_range, train_loss_list, label='Training Loss') 
plt.plot(epochs_range, val_loss_list, label='Validation Loss') 
plt.legend(loc='upper right')
plt.title('Training and Validation Loss') 
plt.savefig('./acc-loss.jpg')

import torch.utils.data.distributed
import torchvision.transforms as transforms 
import torchvision.datasets as datasets 
from torch.autograd import Variable
import torchvision.models as models
from torch.serialization import add_safe_globals

classes = ('cat', 'dog') #测试集数据增强
transform_test = transforms.Compose([ transforms.Resize((224, 224)), transforms.ToTensor(),
transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
])

DEVICE = torch.device("cuda:0" if torch.cuda.is_available() else "cpu") 
# ✅ 添加可信模型类
add_safe_globals([models.resnet.ResNet])

# ✅ 然后加载模型（注意设置 weights_only=False）
model = torch.load("model.pth", weights_only=False)
model.eval() 
model.to(DEVICE)

dataset_test = datasets.ImageFolder('./dataset/test', transform_test) 
print(len(dataset_test)) # 对应文件夹的label

y_true, y_sore = [], []
for index in range(len(dataset_test)): 
    item = dataset_test[index]
    img, label = item #img 是这张图的张量，label 是它的真实类别编号
    img.unsqueeze_(0) #给图像“扩一维”变成 batch 输入格式
    data = img.to(DEVICE) 
    output = model(data)
    _, pred = torch.max(output.data, 1) 
    y_true.append(label) 
    y_sore.append(pred.data.item()) 
    print('Image Name:{}, label:{},predict:{}'.format(
        dataset_test.imgs[index][0], classes[label], classes[pred.data.item()]))

from sklearn.metrics import roc_curve, auc

print(y_true, y_sore)
fpr, tpr, thresholds = roc_curve(y_true, y_sore) 
roc_auc = auc(fpr, tpr)
plt.figure()
plt.title('Receiver Operating Characteristic') 
plt.plot(fpr, tpr, '#9400D3',label=u'AUC = %0.3f'% roc_auc)
plt.legend(loc='lower right') 
plt.plot([0,1],[0,1],'r--')
plt.xlim([-0.1,1.1])
plt.ylim([-0.1,1.1]) 
plt.ylabel('True Positive Rate') 
plt.xlabel('False Positive Rate') 
plt.show() 
plt.savefig('./roc.jpg')

import seaborn as sns
from sklearn.metrics import confusion_matrix 
import matplotlib.pyplot as plt

f,ax=plt.subplots()
C2= confusion_matrix(y_true, y_sore, labels=[0,1])#{'cat': 0, 'dog': 1} print(C2)
print(C2.ravel()) 
sns.heatmap(C2,annot=True,ax=ax,cmap="YlGnBu")

ax.set_title('sns_heatmap_confusion_matrix')#热力图混淆矩阵 ax.set_xlabel( 'Pred')
ax.set_ylabel('True') 
f.savefig('confusion_matrix.jpg', bbox_inches='tight')

